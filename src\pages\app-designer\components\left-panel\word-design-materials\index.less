.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  flex-shrink: 0;
  height: 40px;
  background-color: #e8f3ff;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.contentWrapper {
  flex-grow: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.subFormTabs {
  flex-shrink: 0;
  padding: 12px 10px;
}

.content {
  flex-grow: 1;
  overflow: auto;
  padding: 8px 10px;
  display: flex;
  flex-direction: column;

  :global {
    .ant-form-item {
      margin-bottom: 0;

      .ant-form-item-label {
        > label {
          width: 100%;

          &::after {
            display: none;
          }
        }
      }
    }
  }

  .title {
    font-weight: bold;
    padding: 0 6px;
  }

  .formItemList {
    display: flex;
    flex-wrap: wrap;
    row-gap: 6px;

    .formItemWrapper {
      position: relative;
      padding: 6px;

      .labelWrapper {
        flex-grow: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .combineTagWrapper {
    margin-top: 10px;
  }

  .dataTableList {
    margin-top: 30px;
    .dataTableWrapper {
      margin-top: 30px;
    }
  }

  .emptyWrapper {
    justify-self: center;
  }
}

.footer {
  flex-shrink: 0;
  height: 50px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  box-shadow: 2px 0 4px 0 rgba(0, 0, 0, 10%);
}
